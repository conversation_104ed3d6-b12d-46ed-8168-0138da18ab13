{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "User Action Logs" %} | Legend Fitness Club{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with Back Button -->
        <div class="flex justify-between items-center mb-4">
            <div>
                <h3 class="text-2xl font-bold">{% trans "User Action Logs" %}</h3>
                <p class="text-gray-600 text-sm mt-1">{% trans "Comprehensive security audit log of all user actions across the system" %}</p>
            </div>
            <a href="{% url 'user:index' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                <i class="fas fa-arrow-left mr-2"></i>{% trans "Back to Users" %}
            </a>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <i class="fas fa-list text-blue-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-600">{% trans "Total Logs" %}</p>
                        <p class="text-lg font-semibold text-gray-900">{{ total_logs|default:0 }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <i class="fas fa-check-circle text-green-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-600">{% trans "Success Rate" %}</p>
                        <p class="text-lg font-semibold text-gray-900">95%</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 rounded-lg">
                        <i class="fas fa-users text-yellow-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-600">{% trans "Active Users" %}</p>
                        <p class="text-lg font-semibold text-gray-900">{{ all_users.count }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center">
                    <div class="p-2 bg-red-100 rounded-lg">
                        <i class="fas fa-exclamation-triangle text-red-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-600">{% trans "Critical Actions" %}</p>
                        <p class="text-lg font-semibold text-gray-900">{{ logs|length }}</p>
                    </div>
                </div>
            </div>
        </div>



        <!-- Active Filters Display -->
        {% if selected_action_type or selected_module or selected_user_id or selected_status or search_query or date_from or date_to %}
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h5 class="text-sm font-semibold text-blue-800 mb-2">{% trans "Active Filters:" %}</h5>
            <div class="flex flex-wrap gap-2">
                {% if selected_action_type %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {% trans "Action Type" %}: {{ selected_action_type|title }}
                        <a href="?{% if selected_module %}module={{ selected_module }}&{% endif %}{% if selected_user_id %}user_id={{ selected_user_id }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}{% if search_query %}search={{ search_query }}&{% endif %}{% if date_from %}date_from={{ date_from }}&{% endif %}{% if date_to %}date_to={{ date_to }}{% endif %}" class="ml-1 text-blue-600 hover:text-blue-800">×</a>
                    </span>
                {% endif %}
                {% if selected_module %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {% trans "Module" %}: {{ selected_module|title }}
                        <a href="?{% if selected_action_type %}action_type={{ selected_action_type }}&{% endif %}{% if selected_user_id %}user_id={{ selected_user_id }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}{% if search_query %}search={{ search_query }}&{% endif %}{% if date_from %}date_from={{ date_from }}&{% endif %}{% if date_to %}date_to={{ date_to }}{% endif %}" class="ml-1 text-green-600 hover:text-green-800">×</a>
                    </span>
                {% endif %}
                {% if selected_status %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        {% trans "Status" %}: {{ selected_status|title }}
                        <a href="?{% if selected_action_type %}action_type={{ selected_action_type }}&{% endif %}{% if selected_module %}module={{ selected_module }}&{% endif %}{% if selected_user_id %}user_id={{ selected_user_id }}&{% endif %}{% if search_query %}search={{ search_query }}&{% endif %}{% if date_from %}date_from={{ date_from }}&{% endif %}{% if date_to %}date_to={{ date_to }}{% endif %}" class="ml-1 text-yellow-600 hover:text-yellow-800">×</a>
                    </span>
                {% endif %}
                {% if search_query %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        {% trans "Search" %}: "{{ search_query }}"
                        <a href="?{% if selected_action_type %}action_type={{ selected_action_type }}&{% endif %}{% if selected_module %}module={{ selected_module }}&{% endif %}{% if selected_user_id %}user_id={{ selected_user_id }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}{% if date_from %}date_from={{ date_from }}&{% endif %}{% if date_to %}date_to={{ date_to }}{% endif %}" class="ml-1 text-purple-600 hover:text-purple-800">×</a>
                    </span>
                {% endif %}
            </div>
            <p class="text-xs text-blue-600 mt-2">{% trans "Showing" %} {{ total_logs }} {% trans "filtered results" %}</p>
        </div>
        {% endif %}

        <!-- Filters Section -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h4 class="text-lg font-semibold mb-4">{% trans "Filter Logs" %}</h4>
            <form method="get" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Search -->
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-1">{% trans "Search" %}</label>
                        <input type="text" id="search" name="search" value="{{ search_query }}"
                               placeholder="{% trans 'Search description, user...' %}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <!-- User Filter -->
                    <div>
                        <label for="user_id" class="block text-sm font-medium text-gray-700 mb-1">{% trans "User" %}</label>
                        <select id="user_id" name="user_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">{% trans "All Users" %}</option>
                            {% for user in all_users %}
                                <option value="{{ user.id }}" {% if user.id|stringformat:"s" == selected_user_id %}selected{% endif %}>
                                    {{ user.username }} - {{ user.name|default:user.username }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Action Type Filter -->
                    <div>
                        <label for="action_type" class="block text-sm font-medium text-gray-700 mb-1">{% trans "Action Type" %}</label>
                        <select id="action_type" name="action_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">{% trans "All Actions" %}</option>
                            {% for action_code, action_name in action_types %}
                                <option value="{{ action_code }}" {% if action_code == selected_action_type %}selected{% endif %}>
                                    {{ action_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Module Filter -->
                    <div>
                        <label for="module" class="block text-sm font-medium text-gray-700 mb-1">{% trans "Module" %}</label>
                        <select id="module" name="module" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">{% trans "All Modules" %}</option>
                            {% for module_code, module_name in modules %}
                                <option value="{{ module_code }}" {% if module_code == selected_module %}selected{% endif %}>
                                    {{ module_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Status Filter -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">{% trans "Status" %}</label>
                        <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">{% trans "All Status" %}</option>
                            {% for status_code, status_name in status_choices %}
                                <option value="{{ status_code }}" {% if status_code == selected_status %}selected{% endif %}>
                                    {{ status_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Date From -->
                    <div>
                        <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">{% trans "Date From" %}</label>
                        <input type="date" id="date_from" name="date_from" value="{{ date_from }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <!-- Date To -->
                    <div>
                        <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">{% trans "Date To" %}</label>
                        <input type="date" id="date_to" name="date_to" value="{{ date_to }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <div class="flex space-x-3">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition duration-200">
                        <i class="fas fa-search mr-2"></i>{% trans "Apply Filters" %}
                    </button>
                    <a href="{% url 'user:user_action_logs' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded transition duration-200">
                        <i class="fas fa-times mr-2"></i>{% trans "Clear Filters" %}
                    </a>
                </div>
            </form>
        </div>

        <!-- Logs Table -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h4 class="text-lg font-semibold">{% trans "Action Log Entries" %}</h4>
                {% if page_obj.paginator.count %}
                    <p class="text-sm text-gray-600 mt-1">
                        {% trans "Showing" %} {{ page_obj.start_index }} - {{ page_obj.end_index }} {% trans "of" %} {{ page_obj.paginator.count }} {% trans "entries" %}
                    </p>
                {% endif %}
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "Time" %}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "User" %}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "Action" %}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "Module" %}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "Status" %}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "Target" %}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "Description" %}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "IP Address" %}</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for log in page_obj %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ log.action_time|date:"Y-m-d H:i:s" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ log.user.username|default:"Unknown" }}
                                    </div>
                                    {% if log.user.name %}
                                        <div class="text-sm text-gray-500 ml-1">
                                            ({{ log.user.name }})
                                        </div>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if 'delete' in log.action_type %}bg-red-100 text-red-800
                                    {% elif 'edit' in log.action_type %}bg-yellow-100 text-yellow-800
                                    {% elif 'create' in log.action_type %}bg-green-100 text-green-800
                                    {% else %}bg-blue-100 text-blue-800{% endif %}">
                                    {{ log.get_action_type_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ log.get_module_display }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if log.status == 'success' %}bg-green-100 text-green-800
                                    {% elif log.status == 'failed' %}bg-red-100 text-red-800
                                    {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                    {{ log.get_status_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                {% if log.target_description %}
                                    <div class="max-w-xs truncate" title="{{ log.target_description }}">
                                        {{ log.target_description }}
                                    </div>
                                {% elif log.target_model and log.target_id %}
                                    {{ log.target_model }} #{{ log.target_id }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                {% if log.description %}
                                    <div class="max-w-xs truncate" title="{{ log.description }}">
                                        {{ log.description }}
                                    </div>
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ log.ip_address|default:"-" }}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                                <div class="flex flex-col items-center justify-center py-8">
                                    <i class="fas fa-search text-4xl text-gray-300 mb-4"></i>
                                    <p class="text-lg font-medium">{% trans "No logs found" %}</p>
                                    <p class="text-sm">{% trans "Try adjusting your filters or search criteria" %}</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.user_id %}&user_id={{ request.GET.user_id }}{% endif %}{% if request.GET.action_type %}&action_type={{ request.GET.action_type }}{% endif %}{% if request.GET.module %}&module={{ request.GET.module }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}"
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            {% trans "Previous" %}
                        </a>
                    {% endif %}
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.user_id %}&user_id={{ request.GET.user_id }}{% endif %}{% if request.GET.action_type %}&action_type={{ request.GET.action_type }}{% endif %}{% if request.GET.module %}&module={{ request.GET.module }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}"
                           class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            {% trans "Next" %}
                        </a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            {% trans "Showing" %} <span class="font-medium">{{ page_obj.start_index }}</span> {% trans "to" %} <span class="font-medium">{{ page_obj.end_index }}</span> {% trans "of" %} <span class="font-medium">{{ page_obj.paginator.count }}</span> {% trans "results" %}
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            {% if page_obj.has_previous %}
                                <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.user_id %}&user_id={{ request.GET.user_id }}{% endif %}{% if request.GET.action_type %}&action_type={{ request.GET.action_type }}{% endif %}{% if request.GET.module %}&module={{ request.GET.module }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}"
                                   class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">{% trans "Previous" %}</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                        {{ num }}
                                    </span>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <a href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.user_id %}&user_id={{ request.GET.user_id }}{% endif %}{% if request.GET.action_type %}&action_type={{ request.GET.action_type }}{% endif %}{% if request.GET.module %}&module={{ request.GET.module }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}"
                                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                        {{ num }}
                                    </a>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                                <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.user_id %}&user_id={{ request.GET.user_id }}{% endif %}{% if request.GET.action_type %}&action_type={{ request.GET.action_type }}{% endif %}{% if request.GET.module %}&module={{ request.GET.module }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}"
                                   class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">{% trans "Next" %}</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock body %}

{% block js %}
<script>
    // Auto-submit form when filters change (optional)
    document.addEventListener('DOMContentLoaded', function() {
        const filterForm = document.querySelector('form');
        const selectElements = filterForm.querySelectorAll('select');

        selectElements.forEach(select => {
            select.addEventListener('change', function() {
                // Optional: Auto-submit on filter change
                // filterForm.submit();
            });
        });
    });
</script>
{% endblock js %}
