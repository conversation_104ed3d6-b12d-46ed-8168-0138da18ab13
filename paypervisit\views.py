from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from datetime import datetime, timedelta
from .models import PayPerVisit, PayPerVisitSettings
from user.models import MetaData, User
from core.utils import generate_unique_transaction_id
from core.decorators import module_permission_required
from core.logging_utils import log_create_action, log_edit_action, log_delete_action

@login_required
@module_permission_required(module='paypervisit', required_level='view')
def index(request):
    # Default to showing today's records
    today = datetime.now().date()
    start_date = today
    end_date = today
    filter_active = False

    # Get settings object with prices
    settings_obj = PayPerVisitSettings.objects.first()
    if not settings_obj:
        settings_obj = PayPerVisitSettings.objects.create(
            price_per_person=4000,
            price_for_2=8000,
            price_for_5=20000,
            price_for_10=40000
        )

    # Get current price per person
    price_per_person = settings_obj.price_per_person

    # Handle date filter
    if request.method == "GET" and 'start_date' in request.GET and 'end_date' in request.GET:
        try:
            start_date = datetime.strptime(request.GET.get('start_date'), '%Y-%m-%d').date()
            end_date = datetime.strptime(request.GET.get('end_date'), '%Y-%m-%d').date()
            filter_active = True
        except ValueError:
            messages.error(request, "Invalid date format")

    # Handle payment form submission
    if request.method == "POST" and 'amount' in request.POST and 'num_people' in request.POST:
        try:
            num_people = int(request.POST.get("num_people"))

            # Calculate amount based on number of people and appropriate price
            if num_people == 1:
                amount = settings_obj.price_per_person
            elif num_people == 2:
                amount = settings_obj.price_for_2
            elif num_people == 5:
                amount = settings_obj.price_for_5
            elif num_people == 10:
                amount = settings_obj.price_for_10
            else:
                # For other numbers, use the base price
                amount = num_people * settings_obj.price_per_person

            # Generate unique transaction ID
            transaction_id = generate_unique_transaction_id(prefix="LFC-PPV")

            # Get payment method
            payment_method = request.POST.get("payment_method", "cash")

            paypervisit = PayPerVisit.objects.create(
                trxId=transaction_id,
                amount=amount,
                num_people=num_people,
                payment_method=payment_method,
                received_by=request.user
            )

            # Log the pay-per-visit creation
            log_create_action(
                request=request,
                module='paypervisit',
                target_model='PayPerVisit',
                target_id=transaction_id,
                target_description=f'Pay-per-visit {transaction_id} for {num_people} people',
                additional_data={
                    'transaction_id': transaction_id,
                    'amount': amount,
                    'num_people': num_people,
                    'payment_method': payment_method,
                    'received_by': request.user.username
                },
                financial_impact=amount  # Positive because it's income
            )

            # Update funds
            meta = MetaData.objects.last()
            meta.funds += amount
            meta.save()

            # Use the visitor/visitors text based on the number of people
            visitor_text = "visitor" if num_people == 1 else "visitors"

            # Create a message that will be displayed with "Payment Complete" as the title
            messages.success(request, f"Payment of {amount}៛ for {num_people} {visitor_text} has been processed successfully.")
            return redirect('paypervisit:index')

        except ValueError:
            messages.error(request, "Invalid input values")
        except Exception as e:
            messages.error(request, f"Error: {str(e)}")

    # Query pay-per-visits based on date filter
    if filter_active:
        # Add one day to end_date to include the end date in results (since we're filtering by datetime)
        end_date_inclusive = end_date + timedelta(days=1)
        recent_visits = PayPerVisit.objects.filter(
            date__gte=start_date,
            date__lt=end_date_inclusive
        ).order_by('-date')
    else:
        recent_visits = PayPerVisit.objects.all().order_by('-date')[:50]

    # Calculate summary statistics
    total_amount = sum(visit.amount for visit in recent_visits)
    total_people = sum(visit.num_people for visit in recent_visits)

    context = {
        'recent_visits': recent_visits,
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'filter_active': filter_active,
        'total_amount': total_amount,
        'total_people': total_people,
        'price_per_person': price_per_person,
        'settings': settings_obj
    }
    return render(request, 'paypervisit/index.html', context)

@login_required
@module_permission_required(module='paypervisit', required_level='edit')
def settings(request):
    # Get or create settings
    settings_obj, _ = PayPerVisitSettings.objects.get_or_create(
        id=1,  # Force single instance
        defaults={
            'price_per_person': 4000,
            'quick_select_1': 2,
            'quick_select_2': 5,
            'quick_select_3': 10,
            'price_for_2': 8000,
            'price_for_5': 20000,
            'price_for_10': 40000,
            'custom_price_1': 8000,
            'custom_price_2': 20000,
            'custom_price_3': 40000
        }
    )

    # Handle form submission
    if request.method == "POST":
        try:
            # Get values from form
            price_per_person = int(request.POST.get("price_per_person", 0))

            # Get quick selection people counts
            quick_select_1 = int(request.POST.get("quick_select_1", 0))
            quick_select_2 = int(request.POST.get("quick_select_2", 0))
            quick_select_3 = int(request.POST.get("quick_select_3", 0))

            # Get custom prices
            custom_price_1 = int(request.POST.get("custom_price_1", 0))
            custom_price_2 = int(request.POST.get("custom_price_2", 0))
            custom_price_3 = int(request.POST.get("custom_price_3", 0))

            # Keep backward compatibility with old price fields
            price_for_2 = int(request.POST.get("price_for_2", 0)) if "price_for_2" in request.POST else custom_price_1
            price_for_5 = int(request.POST.get("price_for_5", 0)) if "price_for_5" in request.POST else custom_price_2
            price_for_10 = int(request.POST.get("price_for_10", 0)) if "price_for_10" in request.POST else custom_price_3

            # Validate values
            if (price_per_person <= 0 or
                quick_select_1 <= 1 or quick_select_2 <= 1 or quick_select_3 <= 1 or
                custom_price_1 <= 0 or custom_price_2 <= 0 or custom_price_3 <= 0):
                messages.error(request, "All prices must be greater than zero and people counts must be at least 2")
            else:
                # Update settings
                settings_obj.price_per_person = price_per_person

                # Update quick selection people counts
                settings_obj.quick_select_1 = quick_select_1
                settings_obj.quick_select_2 = quick_select_2
                settings_obj.quick_select_3 = quick_select_3

                # Update custom prices
                settings_obj.custom_price_1 = custom_price_1
                settings_obj.custom_price_2 = custom_price_2
                settings_obj.custom_price_3 = custom_price_3

                # Update backward compatibility fields
                settings_obj.price_for_2 = price_for_2
                settings_obj.price_for_5 = price_for_5
                settings_obj.price_for_10 = price_for_10

                settings_obj.save()

                messages.success(request, "Pay-per-visit settings updated successfully")
                return redirect('paypervisit:settings')
        except ValueError:
            messages.error(request, "Invalid values. Please enter valid numbers.")
        except Exception as e:
            messages.error(request, f"Error: {str(e)}")

    context = {
        'settings': settings_obj
    }
    return render(request, 'paypervisit/settings.html', context)

@login_required
@module_permission_required(module='paypervisit', required_level='view')
def transaction(request):
    """
    Display transaction history for pay-per-visit
    """
    # Default to showing today's records
    today = datetime.now().date()
    start_date = today
    end_date = today
    filter_active = False
    payment_method_filter = None
    cashier_filter = None

    # Handle date filter
    if request.method == "GET":
        # Date filter
        if 'start_date' in request.GET and 'end_date' in request.GET:
            try:
                start_date = datetime.strptime(request.GET.get('start_date'), '%Y-%m-%d').date()
                end_date = datetime.strptime(request.GET.get('end_date'), '%Y-%m-%d').date()
                filter_active = True
            except ValueError:
                messages.error(request, "Invalid date format")

        # Payment method filter
        if 'payment_method' in request.GET and request.GET.get('payment_method'):
            payment_method_filter = request.GET.get('payment_method')
            filter_active = True

        # Cashier filter
        if 'cashier' in request.GET and request.GET.get('cashier'):
            cashier_filter = request.GET.get('cashier')
            filter_active = True

    # Start with all transactions
    transactions_query = PayPerVisit.objects.all()

    # Apply date filter if active
    if 'start_date' in request.GET and 'end_date' in request.GET:
        # Add one day to end_date to include the end date in results (since we're filtering by datetime)
        end_date_inclusive = end_date + timedelta(days=1)
        transactions_query = transactions_query.filter(
            date__gte=start_date,
            date__lt=end_date_inclusive
        )

    # Apply payment method filter if selected
    if payment_method_filter:
        transactions_query = transactions_query.filter(payment_method=payment_method_filter)

    # Apply cashier filter if selected
    if cashier_filter:
        transactions_query = transactions_query.filter(received_by_id=cashier_filter)

    # Order by date (newest first) and limit to 100 if no filter is active
    if filter_active:
        transactions = transactions_query.order_by('-date')
    else:
        transactions = transactions_query.order_by('-date')[:100]

    # Get all cashiers (users who have processed pay-per-visit transactions)
    cashiers = User.objects.filter(
        id__in=PayPerVisit.objects.values_list('received_by', flat=True).distinct()
    ).order_by('username')

    # Calculate summary statistics
    total_amount = sum(visit.amount for visit in transactions)
    total_people = sum(visit.num_people for visit in transactions)

    context = {
        'transactions': transactions,
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'filter_active': filter_active,
        'total_amount': total_amount,
        'total_people': total_people,
        'transaction_count': len(transactions),
        'payment_methods': PayPerVisit.PAYMENT_METHOD_CHOICES,
        'cashiers': cashiers,
        'payment_method_filter': payment_method_filter,
        'cashier_filter': cashier_filter
    }
    return render(request, 'paypervisit/transaction.html', context)

@login_required
@module_permission_required(module='paypervisit', required_level='full')
def delete_paypervisit(request, pk):
    """
    Delete a pay-per-visit transaction and reverse its financial impact
    """
    transaction = get_object_or_404(PayPerVisit, pk=pk)

    # Store information for success message
    transaction_id = transaction.trxId
    amount = transaction.amount
    num_people = transaction.num_people
    received_by = transaction.received_by.username if transaction.received_by else "Unknown"

    try:
        # Log the pay-per-visit deletion before deleting
        log_delete_action(
            request=request,
            module='paypervisit',
            target_model='PayPerVisit',
            target_id=transaction_id,
            target_description=f'Pay-per-visit {transaction_id} for {num_people} people',
            additional_data={
                'transaction_id': transaction_id,
                'amount': amount,
                'num_people': num_people,
                'payment_method': transaction.payment_method,
                'received_by': received_by,
                'date': transaction.date.strftime('%Y-%m-%d %H:%M:%S')
            },
            financial_impact=-amount  # Negative because we're reversing income
        )

        # Reverse the financial impact by subtracting the amount from funds
        meta = MetaData.objects.last()
        if meta:
            meta.funds -= amount
            meta.save()

        # Delete the transaction
        transaction.delete()

        # Format amount for display
        formatted_amount = f"{amount:,}៛"

        # Use the visitor/visitors text based on the number of people
        visitor_text = "visitor" if num_people == 1 else "visitors"

        messages.success(
            request,
            f"Pay-per-visit transaction {transaction_id} deleted successfully! "
            f"Amount {formatted_amount} for {num_people} {visitor_text} has been deducted from gym funds. "
            f"(Originally processed by: {received_by})"
        )

    except Exception as e:
        messages.error(request, f"Error deleting pay-per-visit transaction {transaction_id}: {str(e)}")

    return redirect('paypervisit:transaction')

@login_required
@module_permission_required(module='paypervisit', required_level='view')
def print_receipt(request, pk):
    """
    Print receipt for a pay-per-visit transaction
    """
    # Handle preview from POS interface
    if pk == 0 and request.method == 'POST':
        # Create a temporary transaction object for preview
        transaction = PayPerVisit(
            trxId=request.POST.get('trxId', 'LFC-PPV-PREVIEW'),
            amount=int(request.POST.get('amount', 0)),
            num_people=int(request.POST.get('num_people', 1)),
            payment_method=request.POST.get('payment_method', 'cash'),
            date=datetime.now(),
            received_by=request.user
        )
        is_preview = True
    else:
        # Get an existing transaction
        transaction = get_object_or_404(PayPerVisit, pk=pk)
        is_preview = False

    context = {
        'transaction': transaction,
        'is_preview': is_preview
    }
    return render(request, 'paypervisit/print_receipt.html', context)


