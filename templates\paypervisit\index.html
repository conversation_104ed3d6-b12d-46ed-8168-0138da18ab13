{% extends 'base.html' %}

{% load static %}
{% load custom_filters %}
{% load currency_filters %}



{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/paypervisit.css' %}">
{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">

    <div class="componentWrapper">
        <!-- POS-style Pay-per-visit Interface -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Left Panel: Customer Input -->
            <div class="md:col-span-2">
                <div class="bg-white p-4 rounded shadow-md mb-4">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-2xl font-bold">Pay-per-visit POS</h3>
                        <div class="flex space-x-3">
                            <a href="{% url 'paypervisit:transaction' %}" class="text-blue-900 hover:underline text-sm">
                                <i class="fas fa-history mr-1"></i> Transaction History
                            </a>
                            <a href="{% url 'paypervisit:settings' %}" class="text-blue-900 hover:underline text-sm">
                                <i class="fas fa-cog mr-1"></i> Price Settings
                            </a>
                        </div>
                    </div>

                    <!-- Quick Selection Buttons -->
                    <div class="mb-4">
                        <h4 class="text-lg font-semibold mb-2">Quick Selection</h4>
                        <div class="grid grid-cols-4 gap-2">
                            <button type="button" class="people-btn bg-blue-100 hover:bg-blue-200 text-blue-900 font-bold py-4 px-4 rounded" data-people="1">1 Person</button>
                            <button type="button" class="people-btn bg-blue-100 hover:bg-blue-200 text-blue-900 font-bold py-4 px-4 rounded" data-people="{{ settings.quick_select_1 }}">{{ settings.quick_select_1 }} People</button>
                            <button type="button" class="people-btn bg-blue-100 hover:bg-blue-200 text-blue-900 font-bold py-4 px-4 rounded" data-people="{{ settings.quick_select_2 }}">{{ settings.quick_select_2 }} People</button>
                            <button type="button" class="people-btn bg-blue-100 hover:bg-blue-200 text-blue-900 font-bold py-4 px-4 rounded" data-people="{{ settings.quick_select_3 }}">{{ settings.quick_select_3 }} People</button>
                        </div>
                    </div>

                    <!-- Custom Number Input with Numpad -->
                    <div class="mb-4">
                        <h4 class="text-lg font-semibold mb-2">Custom Number</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <!-- Number Display -->
                            <div class="md:col-span-1">
                                <div class="relative">
                                    <input class="border w-full p-4 text-2xl font-bold leading-tight bg-slate-100 pr-12 rounded"
                                           id="num_people_display"
                                           type="number"
                                           min="1"
                                           placeholder="Number of People"
                                           value="1" />
                                    <div class="absolute inset-y-0 right-0 flex flex-col">
                                        <button type="button" id="increment-btn" class="h-1/2 px-3 bg-gray-200 hover:bg-gray-300 border-l"><i class="fas fa-chevron-up"></i></button>
                                        <button type="button" id="decrement-btn" class="h-1/2 px-3 bg-gray-200 hover:bg-gray-300 border-l border-t"><i class="fas fa-chevron-down"></i></button>
                                    </div>
                                </div>
                                <p class="text-sm text-gray-500 mt-1">1 person = {{ price_per_person|format_khr }}</p>
                            </div>

                            <!-- Numpad -->
                            <div class="md:col-span-2">
                                <div class="grid grid-cols-3 gap-2">
                                    <!-- Numbers 1-9 -->
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border text-xl font-bold py-3 rounded" data-value="1">1</button>
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border text-xl font-bold py-3 rounded" data-value="2">2</button>
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border text-xl font-bold py-3 rounded" data-value="3">3</button>
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border text-xl font-bold py-3 rounded" data-value="4">4</button>
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border text-xl font-bold py-3 rounded" data-value="5">5</button>
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border text-xl font-bold py-3 rounded" data-value="6">6</button>
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border text-xl font-bold py-3 rounded" data-value="7">7</button>
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border text-xl font-bold py-3 rounded" data-value="8">8</button>
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border text-xl font-bold py-3 rounded" data-value="9">9</button>
                                    <!-- Last row -->
                                    <button type="button" id="numpad-clear" class="bg-red-100 hover:bg-red-200 border text-red-800 text-xl font-bold py-3 rounded">C</button>
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border text-xl font-bold py-3 rounded" data-value="0">0</button>
                                    <button type="button" id="numpad-enter" class="bg-blue-100 hover:bg-blue-200 border text-blue-800 text-xl font-bold py-3 rounded"><i class="fas fa-check"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Summary -->
                    <div class="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <h4 class="text-lg font-semibold text-blue-900 mb-3">Payment Summary</h4>

                        <div class="grid grid-cols-2 gap-3">
                            <!-- Visitors -->
                            <div class="bg-white p-3 rounded-lg border border-blue-100">
                                <p class="text-sm text-gray-600 mb-1">Number of Visitors</p>
                                <div class="flex items-center">
                                    <i class="fas fa-users text-xl text-blue-700 mr-2"></i>
                                    <span id="people-display" class="text-xl font-bold">1 person</span>
                                </div>
                            </div>

                            <!-- Rate -->
                            <div class="bg-white p-3 rounded-lg border border-blue-100">
                                <p class="text-sm text-gray-600 mb-1">Rate per Person</p>
                                <div class="flex items-center">
                                    <i class="fas fa-tag text-xl text-blue-700 mr-2"></i>
                                    <span class="text-xl font-bold">{{ price_per_person|format_khr }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Total -->
                        <div class="mt-4 p-3 bg-blue-600 rounded-lg text-white">
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-semibold">Total Amount:</span>
                                <span id="total-display" class="text-2xl font-bold">{{ price_per_person }}៛</span>
                            </div>
                            <p class="text-xs text-blue-100 mt-1">This is the total amount to be paid for all visitors</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel: Payment Summary and Checkout -->
            <div class="md:col-span-1">
                <div class="bg-white p-4 rounded shadow-md sticky top-4">
                    <h3 class="text-xl font-bold mb-2">Payment Details</h3>
                    <p class="text-gray-600 text-sm mb-4">Enter the number of visitors and process payment</p>

                    <form method="post" id="payment-form">
                        {% csrf_token %}
                        <!-- Hidden inputs for form submission -->
                        <input type="hidden" id="num_people" name="num_people" value="1" />
                        <input type="hidden" id="amount" name="amount" value="{{ price_per_person }}" />

                        <!-- Payment Method Selection -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1">Payment Method*</label>
                            <select name="payment_method" class="border w-full p-2 leading-tight bg-slate-100 rounded" required>
                                <option value="cash">Cash</option>
                                <option value="bank">Bank Transfer</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <!-- Receipt Preview -->
                        <div class="receipt-preview" id="receipt-preview">
                            <div class="receipt-paper">
                                <div class="receipt-header">
                                    <div class="receipt-logo">Legend Fitness Club</div>
                                    <h3 class="receipt-title">Pay-per-visit Receipt</h3>
                                    <p class="receipt-subtitle">Kompongkrobey, Svaypoa, Battambang, Cambodia</p>
                                    <p class="receipt-subtitle">Tel: 070 201 530</p>
                                    <p class="receipt-subtitle" id="receipt-date">{% now "F j, Y H:i" %}</p>
                                    <p class="receipt-subtitle" id="receipt-id">Transaction ID: <span id="receipt-trx-id">-</span></p>
                                </div>

                                <div class="receipt-divider"></div>
                                <div class="receipt-row">
                                    <span>Cashier:</span>
                                    <span id="receipt-cashier">{{ request.user.username }}</span>
                                </div>

                                <div class="receipt-divider"></div>

                                <div class="receipt-row">
                                    <span>Payment Method:</span>
                                    <span id="receipt-payment-method">Cash</span>
                                </div>

                                <div class="receipt-row">
                                    <span>Visitors:</span>
                                    <span id="receipt-people">1 person</span>
                                </div>
                                <div class="receipt-row">
                                    <span>Rate:</span>
                                    <span>{{ price_per_person|format_khr }} per person</span>
                                </div>

                                <div class="receipt-divider"></div>



                                <div class="receipt-total">
                                    <span>TOTAL:</span>
                                    <span id="receipt-total">{{ price_per_person }}៛</span>
                                </div>

                                <div class="receipt-footer">

                                    <p>Thank you for visiting Legend Fitness Club!</p>
                                    <p>Telegram: @LegendFitness</p>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="grid grid-cols-2 gap-2 mb-4">
                            <button type="button" id="clear-btn" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-2 rounded flex items-center justify-center">
                                <i class="fas fa-times mr-1"></i> Clear
                            </button>
                            <button type="submit" id="process-btn" class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-3 px-2 rounded flex items-center justify-center">
                                <i class="fas fa-check-circle mr-1"></i> Process
                            </button>
                        </div>

                        <!-- Receipt Buttons -->
                        <div class="mb-4">
                            <button type="button" id="print-btn" class="print-button w-full">
                                <i class="fas fa-print print-button-icon"></i> Print Receipt
                            </button>
                        </div>

                        <!-- Keyboard Shortcuts Help -->
                        <div class="bg-blue-50 p-2 rounded-lg border border-blue-200 mb-4">
                            <p class="text-xs text-blue-800 mb-1"><i class="fas fa-keyboard mr-1"></i> <strong>Keyboard Shortcuts:</strong>"</p>
                            <div class="grid grid-cols-2 gap-1 text-xs text-blue-700">
                                <div><kbd class="bg-white text-blue-700 border px-1 rounded">+</kbd> Increase</div>
                                <div><kbd class="bg-white text-blue-700 border px-1 rounded">-</kbd> Decrease</div>
                                <div><kbd class="bg-white text-blue-700 border px-1 rounded">Enter</kbd> Process</div>
                                <div><kbd class="bg-white text-blue-700 border px-1 rounded">Esc</kbd> Clear</div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Form elements
                const paymentForm = document.getElementById('payment-form');
                const numPeopleDisplay = document.getElementById('num_people_display');
                const numPeopleInput = document.getElementById('num_people');
                const amountInput = document.getElementById('amount');

                // Display elements
                const peopleDisplay = document.getElementById('people-display');
                const totalDisplay = document.getElementById('total-display');

                // Receipt elements
                const receiptPreview = document.getElementById('receipt-preview');
                const receiptPeople = document.getElementById('receipt-people');
                const receiptTotal = document.getElementById('receipt-total');
                const receiptDate = document.getElementById('receipt-date');
                const receiptTrxId = document.getElementById('receipt-trx-id');
                const receiptPaymentMethod = document.getElementById('receipt-payment-method');

                // Buttons
                const incrementBtn = document.getElementById('increment-btn');
                const decrementBtn = document.getElementById('decrement-btn');
                const clearBtn = document.getElementById('clear-btn');
                const printBtn = document.getElementById('print-btn');
                const processBtn = document.getElementById('process-btn');
                const numpadClearBtn = document.getElementById('numpad-clear');
                const numpadEnterBtn = document.getElementById('numpad-enter');

                // Notification container
                const notificationContainer = document.getElementById('notification-container');

                // Notification System
                function showNotification(type, title, message, duration = 5000) {
                    const notification = document.createElement('div');
                    notification.className = `notification ${type}`;

                    let icon = 'info-circle';
                    if (type === 'success') icon = 'check-circle';
                    if (type === 'error') icon = 'exclamation-circle';
                    if (type === 'warning') icon = 'exclamation-triangle';

                    notification.innerHTML = `
                        <i class="fas fa-${icon} notification-icon"></i>
                        <div class="notification-content">
                            <div class="notification-title">${title}</div>
                            <div class="notification-message">${message}</div>
                        </div>
                        <button type="button" class="notification-close">&times;</button>
                        <div class="notification-progress"></div>
                    `;

                    notificationContainer.appendChild(notification);

                    // Close button functionality
                    const closeBtn = notification.querySelector('.notification-close');
                    closeBtn.addEventListener('click', () => {
                        closeNotification(notification);
                    });

                    // Auto close after duration
                    setTimeout(() => {
                        closeNotification(notification);
                    }, duration);

                    return notification;
                }

                function closeNotification(notification) {
                    notification.classList.add('closing');
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }

                // Constants
                const ratePerPerson = {{ price_per_person }};

                // Quick selection people counts
                const quickSelect1 = {{ settings.quick_select_1 }};
                const quickSelect2 = {{ settings.quick_select_2 }};
                const quickSelect3 = {{ settings.quick_select_3 }};

                // Custom prices
                const customPrice1 = {{ settings.custom_price_1 }};
                const customPrice2 = {{ settings.custom_price_2 }};
                const customPrice3 = {{ settings.custom_price_3 }};

                // Backward compatibility
                const priceFor2 = {{ settings.price_for_2 }};
                const priceFor5 = {{ settings.price_for_5 }};
                const priceFor10 = {{ settings.price_for_10 }};

                // Variables
                let currentInput = '';

                // Function to update all displays
                function updateDisplays(numPeople) {
                    // Ensure numPeople is at least 1
                    numPeople = Math.max(1, numPeople);

                    // Update form inputs
                    numPeopleDisplay.value = numPeople;
                    numPeopleInput.value = numPeople;

                    // Calculate total amount based on number of people
                    let totalAmount;
                    if (numPeople === 1) {
                        totalAmount = ratePerPerson;
                    } else if (numPeople === quickSelect1) {
                        totalAmount = customPrice1;
                    } else if (numPeople === quickSelect2) {
                        totalAmount = customPrice2;
                    } else if (numPeople === quickSelect3) {
                        totalAmount = customPrice3;
                    } else {
                        // For other numbers, use the base price
                        totalAmount = numPeople * ratePerPerson;
                    }
                    amountInput.value = totalAmount;

                    // Update visual displays
                    peopleDisplay.textContent = numPeople === 1 ? '1 person' : `${numPeople} people`;
                    totalDisplay.textContent = formatKHR(totalAmount);

                    // Update receipt preview
                    receiptPeople.textContent = numPeople === 1 ? '1 person' : `${numPeople} people`;
                    receiptTotal.textContent = formatKHR(totalAmount);

                    // Update receipt date
                    receiptDate.textContent = new Date().toLocaleString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });

                    // Update active button
                    updateActiveButton(numPeople);
                }

                // Function to handle numpad input
                function handleNumpadInput(value) {
                    if (currentInput === '0') {
                        currentInput = value;
                    } else {
                        currentInput += value;
                    }

                    const numPeople = parseInt(currentInput) || 1;
                    updateDisplays(numPeople);

                    // Show notification for numpad input
                    const visitorText = numPeople === 1 ? 'visitor' : 'visitors';

                    // Calculate total amount based on number of people
                    let totalAmount;
                    if (numPeople === 1) {
                        totalAmount = ratePerPerson;
                    } else if (numPeople === quickSelect1) {
                        totalAmount = customPrice1;
                    } else if (numPeople === quickSelect2) {
                        totalAmount = customPrice2;
                    } else if (numPeople === quickSelect3) {
                        totalAmount = customPrice3;
                    } else {
                        // For other numbers, use the base price
                        totalAmount = numPeople * ratePerPerson;
                    }

                    showNotification('info', `${numPeople} ${visitorText}`, `Total amount: ${formatKHR(totalAmount)}`, 2000);
                }

                // Function to clear numpad input
                function clearNumpadInput() {
                    currentInput = '';
                    updateDisplays(1);
                    showNotification('info', 'Reset to 1 Visitor', 'Number of visitors has been reset to 1', 3000);
                }

                // Function to print receipt
                function printReceipt() {
                    // Show notification
                    showNotification('info', 'Print Receipt', 'Opening receipt preview...', 3000);

                    // Generate a random transaction ID for preview if not already set
                    if (!receiptTrxId.textContent || receiptTrxId.textContent === 'LFC-PPV-XXXX') {
                        const randomId = 'LFC-PPV-' + Math.floor(Math.random() * 10000).toString().padStart(4, '0');
                        receiptTrxId.textContent = randomId;
                    }

                    // Create a form to submit the receipt data
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '{% url "paypervisit:print_receipt" 0 %}';
                    form.target = '_blank';

                    // Add CSRF token
                    const csrfToken = document.createElement('input');
                    csrfToken.type = 'hidden';
                    csrfToken.name = 'csrfmiddlewaretoken';
                    csrfToken.value = '{{ csrf_token }}';
                    form.appendChild(csrfToken);

                    // Add receipt data
                    const addField = (name, value) => {
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = name;
                        input.value = value;
                        form.appendChild(input);
                    };

                    addField('trxId', receiptTrxId.textContent);
                    addField('num_people', numPeopleInput.value);
                    addField('amount', amountInput.value);
                    addField('date', new Date().toISOString());

                    // Add the form to the document and submit it
                    document.body.appendChild(form);
                    form.submit();
                    document.body.removeChild(form);
                }

                // Function to show processing animation (for UI feedback only)
                function showProcessingAnimation() {
                    // Show notification
                    showNotification('info', 'Processing Payment', 'Processing payment for ' + numPeopleInput.value + ' visitor(s)...', 2000);

                    // Disable the process button and show loading state
                    processBtn.disabled = true;
                    processBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Processing...';

                    // Add success animation to receipt
                    receiptPreview.classList.add('success-animation');

                    // Remove animation class after it completes
                    setTimeout(() => {
                        receiptPreview.classList.remove('success-animation');
                    }, 500);
                }

                // Function to reset the process button after form submission
                function resetProcessButton() {
                    processBtn.disabled = false;
                    processBtn.innerHTML = '<i class="fas fa-check-circle mr-1"></i> Process';
                }

                // Function to handle form submission
                function submitPaymentForm() {
                    // Validate form data
                    const numPeople = parseInt(numPeopleInput.value);
                    const amount = parseInt(amountInput.value);
                    const paymentMethod = paymentMethodSelect.value;

                    if (!numPeople || numPeople < 1) {
                        showNotification('error', 'Invalid Input', 'Number of people must be at least 1', 5000);
                        return false;
                    }

                    if (!amount || amount < 1) {
                        showNotification('error', 'Invalid Input', 'Amount must be greater than 0', 5000);
                        return false;
                    }

                    if (!paymentMethod) {
                        showNotification('error', 'Invalid Input', 'Please select a payment method', 5000);
                        return false;
                    }

                    // Show processing animation
                    showProcessingAnimation();

                    // Submit the form
                    paymentForm.submit();

                    return true;
                }

                // Initialize with default value
                updateDisplays(1);

                // Function to update active button state
                function updateActiveButton(numPeople) {
                    // Remove active class from all buttons
                    document.querySelectorAll('.people-btn').forEach(btn => {
                        btn.classList.remove('bg-blue-500', 'text-white');
                        btn.classList.add('bg-blue-100', 'text-blue-900');
                    });

                    // Add active class to the matching button
                    const activeButton = document.querySelector(`.people-btn[data-people="${numPeople}"]`);
                    if (activeButton) {
                        activeButton.classList.remove('bg-blue-100', 'text-blue-900');
                        activeButton.classList.add('bg-blue-500', 'text-white');
                    }
                }

                // Event listeners for quick selection buttons
                document.querySelectorAll('.people-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const people = parseInt(this.getAttribute('data-people'));
                        currentInput = people.toString();
                        updateDisplays(people);
                        updateActiveButton(people);

                        // Show notification
                        const visitorText = people === 1 ? 'visitor' : 'visitors';

                        // Calculate total amount based on number of people
                        let totalAmount;
                        if (people === 1) {
                            totalAmount = ratePerPerson;
                        } else if (people === quickSelect1) {
                            totalAmount = customPrice1;
                        } else if (people === quickSelect2) {
                            totalAmount = customPrice2;
                        } else if (people === quickSelect3) {
                            totalAmount = customPrice3;
                        } else {
                            // For other numbers, use the base price
                            totalAmount = people * ratePerPerson;
                        }

                        showNotification('info', `${people} ${visitorText} selected`, `Total amount: ${totalAmount}៛`, 2000);
                    });
                });

                // Event listeners for numpad buttons
                document.querySelectorAll('.numpad-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const value = this.getAttribute('data-value');
                        handleNumpadInput(value);
                    });
                });

                // Event listener for numpad clear button
                numpadClearBtn.addEventListener('click', clearNumpadInput);

                // Event listener for numpad enter button
                numpadEnterBtn.addEventListener('click', function() {
                    submitPaymentForm();
                });

                // Event listener for custom number input
                numPeopleDisplay.addEventListener('input', function() {
                    const numPeople = parseInt(this.value) || 1;
                    currentInput = numPeople.toString();
                    updateDisplays(numPeople);
                });

                // Event listeners for increment/decrement buttons
                incrementBtn.addEventListener('click', function() {
                    const currentValue = parseInt(numPeopleDisplay.value) || 0;
                    const newValue = currentValue + 1;
                    currentInput = newValue.toString();
                    updateDisplays(newValue);
                    // Show notification for increment
                    const visitorText = newValue === 1 ? 'visitor' : 'visitors';

                    // Calculate total amount based on number of people
                    let totalAmount;
                    if (newValue === 1) {
                        totalAmount = ratePerPerson;
                    } else if (newValue === quickSelect1) {
                        totalAmount = customPrice1;
                    } else if (newValue === quickSelect2) {
                        totalAmount = customPrice2;
                    } else if (newValue === quickSelect3) {
                        totalAmount = customPrice3;
                    } else {
                        // For other numbers, use the base price
                        totalAmount = newValue * ratePerPerson;
                    }

                    showNotification('info', `${newValue} ${visitorText}`, `Total amount: ${totalAmount}៛`, 2000);
                });

                decrementBtn.addEventListener('click', function() {
                    const currentValue = parseInt(numPeopleDisplay.value) || 2;
                    const newValue = Math.max(1, currentValue - 1);
                    currentInput = newValue.toString();
                    updateDisplays(newValue);
                    // Show notification for decrement
                    const visitorText = newValue === 1 ? 'visitor' : 'visitors';

                    // Calculate total amount based on number of people
                    let totalAmount;
                    if (newValue === 1) {
                        totalAmount = ratePerPerson;
                    } else if (newValue === quickSelect1) {
                        totalAmount = customPrice1;
                    } else if (newValue === quickSelect2) {
                        totalAmount = customPrice2;
                    } else if (newValue === quickSelect3) {
                        totalAmount = customPrice3;
                    } else {
                        // For other numbers, use the base price
                        totalAmount = newValue * ratePerPerson;
                    }

                    showNotification('info', `${newValue} ${visitorText}`, `Total amount: ${totalAmount}៛`, 2000);
                });

                // Event listener for clear button
                clearBtn.addEventListener('click', clearNumpadInput);

                // Event listener for print button
                printBtn.addEventListener('click', printReceipt);

                // Event listener for payment method change
                const paymentMethodSelect = document.querySelector('select[name="payment_method"]');
                paymentMethodSelect.addEventListener('change', function() {
                    // Update receipt payment method display
                    const selectedOption = this.options[this.selectedIndex];
                    receiptPaymentMethod.textContent = selectedOption.text;
                });

                // Event listener for form submission
                paymentForm.addEventListener('submit', function(e) {
                    e.preventDefault(); // Prevent default form submission
                    submitPaymentForm(); // Use our custom submission function
                });

                // Keyboard shortcuts
                document.addEventListener('keydown', function(e) {
                    // Only process keyboard shortcuts if the focus is not in an input field
                    if (e.target.tagName.toLowerCase() === 'input') {
                        return;
                    }

                    // Number keys (0-9)
                    if (e.key >= '0' && e.key <= '9') {
                        handleNumpadInput(e.key);
                    }

                    // Plus key to increment
                    if (e.key === '+' || e.key === '=') {
                        incrementBtn.click();
                    }

                    // Minus key to decrement
                    if (e.key === '-' || e.key === '_') {
                        decrementBtn.click();
                    }

                    // Enter key to process payment
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        submitPaymentForm();
                    }

                    // Escape key to clear
                    if (e.key === 'Escape') {
                        clearNumpadInput();
                    }

                    // P key to print
                    if (e.key === 'p' || e.key === 'P') {
                        printReceipt();
                    }
                });

                // Check for Django messages and convert them to notifications
                {% if messages %}
                    {% for message in messages %}
                        {% if 'success' in message.tags %}
                            setTimeout(() => {
                                showNotification('success', 'Payment Complete', '{{ message }}', 7000);
                                // Reset form after successful payment
                                resetProcessButton();
                                clearNumpadInput();
                                // Generate new transaction ID for receipt
                                const newTrxId = 'LFC-PPV-' + new Date().toISOString().slice(0,10).replace(/-/g,'') + '-' + Math.random().toString(36).substr(2, 8);
                                receiptTrxId.textContent = newTrxId;
                            }, 500);
                        {% elif 'error' in message.tags %}
                            setTimeout(() => {
                                showNotification('error', 'Error', '{{ message }}', 7000);
                                resetProcessButton();
                            }, 500);
                        {% else %}
                            setTimeout(() => {
                                showNotification('info', 'Information', '{{ message }}', 7000);
                                resetProcessButton();
                            }, 500);
                        {% endif %}
                    {% endfor %}
                {% else %}
                    // Show welcome notification if no messages
                    setTimeout(() => {
                        showNotification('info', 'Pay-per-visit System', 'Select the number of visitors using the quick buttons or numpad, then process payment', 5000);
                    }, 1000);
                {% endif %}
            });
        </script>

        <!-- Recent Transactions Link -->
        <div class="mt-6 text-center">
            <a href="{% url 'paypervisit:transaction' %}" class="inline-flex items-center justify-center bg-blue-900 hover:bg-blue-800 text-white font-bold py-3 px-6 rounded-lg transition duration-200">
                <i class="fas fa-history mr-2"></i> View All Transactions
            </a>
        </div>
    </div>
</div>
{% endblock %}
