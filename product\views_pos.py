from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.http import JsonResponse
from django.utils import timezone
from datetime import datetime, timedelta
from django.db.models import Sum, F, ExpressionWrapper, DecimalField
from django.contrib.auth.decorators import login_required
from .models import Product, Category, Sale, SaleItem
from user.models import MetaData, User
from core.utils import generate_unique_transaction_id
from core.decorators import module_permission_required
from core.logging_utils import log_delete_action
import json

def pos(request):
    """
    Point of Sale (POS) system
    """
    categories = Category.objects.all()
    products = Product.objects.filter(quantity__gt=0)

    if request.method == "POST":
        try:
            # Get the cart data from the form
            cart_data = request.POST.get("cart_data")
            payment_method = request.POST.get("payment_method")
            notes = request.POST.get("notes", "")

            if not cart_data:
                messages.error(request, "Cart is empty")
                return redirect('product:pos')

            # Parse the cart data
            cart_items = json.loads(cart_data)

            if not cart_items:
                messages.error(request, "Cart is empty")
                return redirect('product:pos')

            # Calculate total amount
            total_amount = sum(item['total'] for item in cart_items)

            # Generate unique transaction ID
            trx_id = generate_unique_transaction_id(prefix="SALE")

            # Create the sale
            sale = Sale.objects.create(
                trxId=trx_id,
                total_amount=total_amount,
                payment_method=payment_method,
                sold_by=request.user,
                notes=notes
            )

            # Create sale items and update product quantities
            for item in cart_items:
                product = Product.objects.get(id=item['product_id'])
                quantity = item['quantity']

                # Check if we have enough stock
                if product.quantity < quantity:
                    messages.error(request, f"Not enough stock for {product.name}")
                    sale.delete()
                    return redirect('product:pos')

                # Create the sale item
                SaleItem.objects.create(
                    sale=sale,
                    product=product,
                    quantity=quantity,
                    price=item['price']
                )

                # Update product quantity
                product.quantity -= quantity
                product.save()

            # Update gym funds
            meta = MetaData.objects.last()
            meta.funds += total_amount
            meta.save()

            # Format the total amount with the Cambodian Riel symbol
            formatted_amount = f"{total_amount}៛"

            # Create a more complete success message
            messages.success(request, f"Sale completed successfully!  Total Amount: {formatted_amount}")

            return redirect('product:pos')

        except Exception as e:
            messages.error(request, f"Error processing sale: {str(e)}")

    context = {
        'categories': categories,
        'products': products,
    }
    return render(request, 'product/pos.html', context)

def get_products(request):
    """
    API to get products by category
    """
    category_id = request.GET.get('category_id')

    if category_id:
        products = Product.objects.filter(category_id=category_id, quantity__gt=0)
    else:
        products = Product.objects.filter(quantity__gt=0)

    products_data = []
    for product in products:
        products_data.append({
            'id': product.id,
            'name': product.name,
            'price': float(product.retail_price),
            'quantity': product.quantity,
            'image': product.image.url if product.image else None,
        })

    return JsonResponse({'products': products_data})

def print_receipt(request, pk):
    """
    Print sale receipt
    """
    sale = get_object_or_404(Sale, pk=pk)
    context = {
        'sale': sale,
    }
    return render(request, 'product/print_receipt.html', context)

@login_required
@module_permission_required(module='pos', required_level='full')
def delete_sale(request, pk):
    """
    Delete a sale record, reverse its financial impact, and restore product inventory
    """
    sale = get_object_or_404(Sale, pk=pk)

    # Store information for success message
    transaction_id = sale.trxId
    total_amount = sale.total_amount
    sold_by = sale.sold_by.username if sale.sold_by else "Unknown"

    # Get product count and items for the message and inventory restoration
    product_count = sale.items.count()
    sale_items = list(sale.items.select_related('product').all())

    try:
        # First, restore inventory for each sold item BEFORE deleting the sale
        restored_products = []
        for item in sale_items:
            try:
                # Use F() expression to avoid race conditions when updating product quantities
                from django.db.models import F
                from .models import Product

                # Add the sold quantity back to the product's stock
                updated_count = Product.objects.filter(id=item.product.id).update(
                    quantity=F('quantity') + item.quantity
                )

                if updated_count > 0:
                    # Refresh the product to get updated quantity for logging
                    item.product.refresh_from_db()
                    restored_products.append({
                        'name': item.product.name,
                        'quantity': item.quantity,
                        'new_stock': item.product.quantity
                    })
                    print(f"INVENTORY RESTORED: {item.product.name} - Added {item.quantity} units back to stock (New stock: {item.product.quantity})")
                else:
                    print(f"WARNING: Could not restore inventory for product ID {item.product.id} - Product may have been deleted")

            except Exception as inventory_error:
                print(f"ERROR restoring inventory for {item.product.name}: {str(inventory_error)}")
                # Continue with other items even if one fails

        # Reverse the financial impact by subtracting the amount from funds
        meta = MetaData.objects.last()
        if meta:
            meta.funds -= total_amount
            meta.save()

        # Delete the sale (this will cascade delete all sale items)
        sale.delete()

        # Format amount for display
        formatted_amount = f"{total_amount:,}៛"

        # Use the product/products text based on the number of items
        product_text = "product" if product_count == 1 else "products"

        # Create detailed success message including inventory restoration
        success_message = (
            f"Sale {transaction_id} deleted successfully! "
            f"Amount {formatted_amount} for {product_count} {product_text} has been deducted from gym funds "
            f"and product inventory has been restored. "
            f"(Originally sold by: {sold_by})"
        )

        messages.success(request, success_message)

        # Log the action in UserActionLog for security audit
        log_delete_action(
            request=request,
            module='pos',
            target_model='Sale',
            target_id=transaction_id,
            target_description=f'Sale transaction {transaction_id} (Amount: {formatted_amount}, Products: {product_count})',
            financial_impact=-total_amount,  # Negative because funds were deducted
            additional_data={
                'original_sold_by': sold_by,
                'products_restored': len(restored_products),
                'product_details': restored_products,
                'financial_reversal': f'-{formatted_amount}',
                'inventory_restored': True
            }
        )

        # Log the restoration details for debugging
        print(f"SALE DELETION COMPLETE: {transaction_id}")
        print(f"- Financial reversal: -{formatted_amount}")
        print(f"- Products restored: {len(restored_products)}")
        for product in restored_products:
            print(f"  * {product['name']}: +{product['quantity']} units (Stock: {product['new_stock']})")

    except Exception as e:
        messages.error(request, f"Error deleting sale {transaction_id}: {str(e)}")
        print(f"ERROR deleting sale {transaction_id}: {str(e)}")

        # Log the failed action
        log_delete_action(
            request=request,
            module='pos',
            target_model='Sale',
            target_id=transaction_id,
            target_description=f'Sale transaction {transaction_id} (Amount: {total_amount:,}៛)',
            status='failed',
            additional_data={
                'error_message': str(e),
                'original_sold_by': sold_by,
                'attempted_financial_reversal': f'-{total_amount:,}៛'
            }
        )

    return redirect('product:pos_history')

@login_required
@module_permission_required(module='pos', required_level='view')
def history(request):
    """
    Sales History for POS system
    """
    # Default to showing today's sales
    today = datetime.now().date()
    start_date = today
    end_date = today
    filter_active = False
    payment_method_filter = None
    cashier_filter = None

    # Handle date filtering
    if request.method == "GET" and 'start_date' in request.GET and 'end_date' in request.GET:
        try:
            start_date = datetime.strptime(request.GET.get('start_date'), '%Y-%m-%d').date()
            end_date = datetime.strptime(request.GET.get('end_date'), '%Y-%m-%d').date()
            filter_active = True
        except ValueError:
            messages.error(request, "Invalid date format")

    # Handle payment method filter
    if request.method == "GET" and 'payment_method' in request.GET:
        payment_method_filter = request.GET.get('payment_method')
        if payment_method_filter == '':
            payment_method_filter = None

    # Handle cashier filter
    if request.method == "GET" and 'cashier' in request.GET:
        cashier_filter = request.GET.get('cashier')
        if cashier_filter == '':
            cashier_filter = None

    # Start with all sales
    sales_query = Sale.objects.all()

    # Apply date filter if active
    if 'start_date' in request.GET and 'end_date' in request.GET:
        # Add one day to end_date to include the end date in results
        end_date_inclusive = end_date + timedelta(days=1)
        sales_query = sales_query.filter(
            date__gte=start_date,
            date__lt=end_date_inclusive
        )
    elif not filter_active:
        # If no filter is active, default to today's sales
        sales_query = sales_query.filter(date__date=today)

    # Apply payment method filter if selected
    if payment_method_filter:
        sales_query = sales_query.filter(payment_method=payment_method_filter)

    # Apply cashier filter if selected
    if cashier_filter:
        sales_query = sales_query.filter(sold_by_id=cashier_filter)

    # Get the final sales queryset with prefetch_related for items
    sales = sales_query.prefetch_related('items', 'items__product').order_by('-date')

    # Calculate summary statistics
    total_amount = sales.aggregate(Sum('total_amount'))['total_amount__sum'] or 0

    # Get sales by product
    sales_by_product = SaleItem.objects.filter(
        sale__in=sales
    ).values(
        'product__name'
    ).annotate(
        total_quantity=Sum('quantity'),
        total_amount=Sum(ExpressionWrapper(F('quantity') * F('price'), output_field=DecimalField()))
    ).order_by('-total_amount')

    # Get all cashiers (users who have processed sales)
    cashiers = User.objects.filter(
        id__in=Sale.objects.values_list('sold_by', flat=True).distinct()
    ).order_by('username')

    context = {
        'sales': sales,
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'filter_active': filter_active,
        'total_amount': total_amount,
        'sales_by_product': sales_by_product,
        'payment_methods': Sale._meta.get_field('payment_method').choices,
        'cashiers': cashiers,
        'payment_method_filter': payment_method_filter,
        'cashier_filter': cashier_filter
    }
    return render(request, 'product/pos_history.html', context)
